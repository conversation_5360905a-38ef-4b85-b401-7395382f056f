{"name": "start", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier . --write", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@mongez/events": "^2.1.0", "@mongez/http": "^2.2.10", "@mongez/react-atom": "^5.1.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "color": "^5.0.0", "google-map-react": "^2.2.5", "i18next": "^25.3.4", "leaflet": "^1.9.4", "lucide-react": "^0.539.0", "radix-ui": "^1.4.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.1", "react-input-color": "^4.0.1", "react-leaflet": "^5.0.0", "react-resizable-panels": "^3.0.4", "react-router": "^7.8.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/color": "^4.2.0", "@types/google-map-react": "^2.1.10", "@types/leaflet": "^1.9.20", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^3.11.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}