import { authAtom } from "@/atoms/auth-atom";
import Endpoint from "@mongez/http";
import { type AxiosResponse } from "axios";

export const endpoint = new Endpoint({
  baseURL: "https://gps.ipsticket.com/api",

  setAuthorizationHeader: () => {
    if (localStorage.getItem("gps-token")) {
      const token = localStorage.getItem("gps-token");
      return `Bearer ${token}`;
    }
  },

  headers: {
    "Accept-Language": localStorage.getItem("gps-lang") || "en", // default to English
  },
});

endpoint.events.onError((response: AxiosResponse) => {
  if (response.data.status_code === 401) {
    authAtom.change("user", null);
    authAtom.change("token", "");
  }
});
