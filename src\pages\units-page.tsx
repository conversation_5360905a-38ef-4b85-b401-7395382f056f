import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import useUnitsPage from "@/hooks/use-units-page";
import type { ColumnDef } from "@tanstack/react-table";
import type { LatLngExpression } from "leaflet";
import { Edit, Locate, MessageCircle, Trash2, Wifi } from "lucide-react";
import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, TileLayer, useMap } from "react-leaflet";

type Payment = {
  id: string;
  amount: number;
  status: "pending" | "processing" | "success" | "failed";
  email: string;
};

const columns: ColumnDef<Payment>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Status" />;
    },
    cell: ({ row }) => {
      const status = row.original.status;
      return <div>{status}</div>;
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Email" />;
    },
    cell: ({ row }) => {
      const email = row.original.email;
      return <div>{email}</div>;
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Amount" />;
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount);

      return <div>{formatted}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: () => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <MessageCircle />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Message</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Locate />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Track</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Wifi />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Data Accuracy</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Edit />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

const data: Payment[] = [
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "729cbf67-cc84-43b0-b0d9-878b7e7c4fac",
    amount: 100,
    status: "processing",
    email: "<EMAIL>",
  },
];

export default function UnitsPage() {
  const { handleResize, isMobile, tableSize } = useUnitsPage();

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <DataTable columns={columns} data={data} />
        <div className="h-[500px] w-full p-2">
          <UnitsMap />
        </div>
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="p-4"
        defaultSize={tableSize}
        onResize={handleResize}
      >
        <DataTable columns={columns} data={data} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">
        <UnitsMap />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

const UnitsMap = () => {
  const position: LatLngExpression = [26.8206, 30.8025];

  return (
    <MapContainer
      className="z-20 h-full w-full"
      center={position}
      zoom={6}
      scrollWheelZoom={false}
    >
      <ResizeHandler />
      <TileLayer
        className="h-full w-full"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      <Marker position={position}>
        <Popup>
          A pretty CSS3 popup. <br /> Easily customizable.
        </Popup>
      </Marker>
    </MapContainer>
  );
};
