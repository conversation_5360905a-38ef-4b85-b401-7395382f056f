import AddAreaForm from "@/components/areas/add-area-form";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { useIsMobile } from "@/hooks/use-mobile";
import type { LatLngExpression } from "leaflet";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, TileLayer, useMap } from "react-leaflet";

export default function AddAreaPage() {
  const isMobile = useIsMobile();

  const [tableSize, setTableSize] = useState(
    localStorage.getItem("gps-table-size")
      ? JSON.parse(localStorage.getItem("gps-table-size")!)
      : 50,
  );

  const handleResize = (e: number) => {
    setTableSize(e);
    localStorage.setItem("gps-table-size", JSON.stringify(e));
  };

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <AddAreaForm />
        </div>
        <div className="h-[500px] w-full p-2">
          <AddAreaMap />
        </div>
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={30}
      >
        <AddAreaForm />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">
        <AddAreaMap />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

const AddAreaMap = () => {
  const position: LatLngExpression = [26.8206, 30.8025];

  return (
    <MapContainer
      className="z-20 h-full w-full"
      center={position}
      zoom={6}
      scrollWheelZoom={false}
    >
      <ResizeHandler />
      <TileLayer
        className="h-full w-full"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      <Marker position={position}>
        <Popup>
          A pretty CSS3 popup. <br /> Easily customizable.
        </Popup>
      </Marker>
    </MapContainer>
  );
};
