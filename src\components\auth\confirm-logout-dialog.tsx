import { authAtom } from "@/atoms/auth-atom";
import { openConfirmLogoutDialogAtom } from "@/atoms/open-confirm-logout-dialog-atom";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import i18n from "@/localization/i18n";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";

export default function ConfirmLogoutDialog() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { loading } = authAtom.useValue();
  const isOpened = openConfirmLogoutDialogAtom.useOpened();

  const closeDialog = () => {
    openConfirmLogoutDialogAtom.close();
  };

  function logout() {
    authAtom.logout(() => {
      closeDialog();
      navigate(URLS.auth.login);
    });
  }

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>{t("auth.confirm_logout.title")}</DialogTitle>
          <DialogDescription>
            {t("auth.confirm_logout.description")}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" onClick={closeDialog}>
              {t("auth.confirm_logout.stay_logged_in")}
            </Button>
          </DialogClose>
          <Button variant="destructive" onClick={logout} disabled={loading}>
            {loading
              ? t("auth.confirm_logout.logging_out")
              : t("auth.confirm_logout.yes_logout")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
