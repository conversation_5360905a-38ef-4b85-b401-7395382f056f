import { Card } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

export function OverlayLoader() {
  return (
    <div className="bg-background/80 absolute top-1/2 left-1/2 z-50 flex -translate-1/2 items-center justify-center backdrop-blur-sm">
      <Card className="flex flex-col items-center justify-center gap-2 p-6">
        <Loader2 className="text-primary h-8 w-8 animate-spin" />
        <p className="text-muted-foreground text-sm font-medium">Loading...</p>
      </Card>
    </div>
  );
}
